'use client'

import { useState, useEffect, useRef } from 'react'
import { Activity, Zap, Clock, Database, Wifi } from 'lucide-react'

interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  memoryUsage: number
  networkLatency: number
  fps: number
  bundleSize: number
}

interface PerformanceMonitorProps {
  enabled?: boolean
  showOverlay?: boolean
  logToConsole?: boolean
}

export default function PerformanceMonitor({
  enabled = process.env.NODE_ENV === 'development',
  showOverlay = false,
  logToConsole = true
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    networkLatency: 0,
    fps: 0,
    bundleSize: 0
  })
  const [isVisible, setIsVisible] = useState(false)
  const frameCount = useRef(0)
  const lastTime = useRef(performance.now())

  useEffect(() => {
    if (!enabled) return

    // Measure page load time
    const measureLoadTime = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        const loadTime = navigation.loadEventEnd - navigation.fetchStart
        setMetrics(prev => ({ ...prev, loadTime }))
      }
    }

    // Measure render time
    const measureRenderTime = () => {
      const paintEntries = performance.getEntriesByType('paint')
      const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
      if (fcp) {
        setMetrics(prev => ({ ...prev, renderTime: fcp.startTime }))
      }
    }

    // Measure memory usage
    const measureMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        const memoryUsage = memory.usedJSHeapSize / 1024 / 1024 // MB
        setMetrics(prev => ({ ...prev, memoryUsage }))
      }
    }

    // Measure FPS
    const measureFPS = () => {
      const now = performance.now()
      frameCount.current++
      
      if (now - lastTime.current >= 1000) {
        const fps = Math.round((frameCount.current * 1000) / (now - lastTime.current))
        setMetrics(prev => ({ ...prev, fps }))
        frameCount.current = 0
        lastTime.current = now
      }
      
      requestAnimationFrame(measureFPS)
    }

    // Measure network latency
    const measureNetworkLatency = async () => {
      try {
        const start = performance.now()
        await fetch('/api/ping', { method: 'HEAD' }).catch(() => {})
        const latency = performance.now() - start
        setMetrics(prev => ({ ...prev, networkLatency: latency }))
      } catch (error) {
        // Fallback: measure to a known fast endpoint
        try {
          const start = performance.now()
          await fetch('/', { method: 'HEAD' })
          const latency = performance.now() - start
          setMetrics(prev => ({ ...prev, networkLatency: latency }))
        } catch {
          setMetrics(prev => ({ ...prev, networkLatency: -1 }))
        }
      }
    }

    // Estimate bundle size
    const estimateBundleSize = () => {
      const scripts = Array.from(document.querySelectorAll('script[src]'))
      let totalSize = 0
      
      scripts.forEach(script => {
        const src = script.getAttribute('src')
        if (src && src.includes('/_next/')) {
          // Estimate based on typical Next.js bundle sizes
          totalSize += 500 // KB estimate per chunk
        }
      })
      
      setMetrics(prev => ({ ...prev, bundleSize: totalSize }))
    }

    // Initialize measurements
    setTimeout(measureLoadTime, 100)
    setTimeout(measureRenderTime, 100)
    measureMemoryUsage()
    measureNetworkLatency()
    estimateBundleSize()
    requestAnimationFrame(measureFPS)

    // Update memory usage periodically
    const memoryInterval = setInterval(measureMemoryUsage, 5000)
    const networkInterval = setInterval(measureNetworkLatency, 30000)

    return () => {
      clearInterval(memoryInterval)
      clearInterval(networkInterval)
    }
  }, [enabled])

  useEffect(() => {
    if (logToConsole && enabled) {
      console.group('🚀 Performance Metrics')
      console.log('Load Time:', `${metrics.loadTime.toFixed(2)}ms`)
      console.log('Render Time:', `${metrics.renderTime.toFixed(2)}ms`)
      console.log('Memory Usage:', `${metrics.memoryUsage.toFixed(2)}MB`)
      console.log('Network Latency:', `${metrics.networkLatency.toFixed(2)}ms`)
      console.log('FPS:', metrics.fps)
      console.log('Bundle Size:', `~${metrics.bundleSize}KB`)
      console.groupEnd()
    }
  }, [metrics, logToConsole, enabled])

  if (!enabled) return null

  const getPerformanceGrade = () => {
    const { loadTime, renderTime, memoryUsage, fps } = metrics
    let score = 100

    // Deduct points for slow metrics
    if (loadTime > 3000) score -= 20
    else if (loadTime > 1500) score -= 10
    
    if (renderTime > 2000) score -= 20
    else if (renderTime > 1000) score -= 10
    
    if (memoryUsage > 100) score -= 15
    else if (memoryUsage > 50) score -= 8
    
    if (fps < 30) score -= 25
    else if (fps < 50) score -= 10

    if (score >= 90) return { grade: 'A', color: 'text-green-600', bg: 'bg-green-100' }
    if (score >= 80) return { grade: 'B', color: 'text-green-600', bg: 'bg-green-100' }
    if (score >= 70) return { grade: 'C', color: 'text-yellow-600', bg: 'bg-yellow-100' }
    if (score >= 60) return { grade: 'D', color: 'text-orange-600', bg: 'bg-orange-100' }
    return { grade: 'F', color: 'text-red-600', bg: 'bg-red-100' }
  }

  const performanceGrade = getPerformanceGrade()

  return (
    <>
      {/* Performance Toggle Button */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 bg-gray-900 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
        title="Performance Monitor"
      >
        <Activity className="h-5 w-5" />
      </button>

      {/* Performance Overlay */}
      {(isVisible || showOverlay) && (
        <div className="fixed bottom-20 right-4 z-50 bg-white border border-gray-200 rounded-xl shadow-xl p-4 w-80 animate-fade-in">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-heading-small text-gray-900 flex items-center">
              <Zap className="h-4 w-4 mr-2" />
              Performance
            </h3>
            <div className={`px-2 py-1 rounded-full text-xs font-bold ${performanceGrade.bg} ${performanceGrade.color}`}>
              Grade {performanceGrade.grade}
            </div>
          </div>

          {/* Metrics Grid */}
          <div className="space-y-3">
            {/* Load Time */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-body-medium text-gray-700">Load Time</span>
              </div>
              <span className={`text-body-medium font-medium ${
                metrics.loadTime > 3000 ? 'text-red-600' :
                metrics.loadTime > 1500 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {metrics.loadTime.toFixed(0)}ms
              </span>
            </div>

            {/* Render Time */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4 text-gray-500" />
                <span className="text-body-medium text-gray-700">First Paint</span>
              </div>
              <span className={`text-body-medium font-medium ${
                metrics.renderTime > 2000 ? 'text-red-600' :
                metrics.renderTime > 1000 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {metrics.renderTime.toFixed(0)}ms
              </span>
            </div>

            {/* Memory Usage */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Database className="h-4 w-4 text-gray-500" />
                <span className="text-body-medium text-gray-700">Memory</span>
              </div>
              <span className={`text-body-medium font-medium ${
                metrics.memoryUsage > 100 ? 'text-red-600' :
                metrics.memoryUsage > 50 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {metrics.memoryUsage.toFixed(1)}MB
              </span>
            </div>

            {/* Network Latency */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Wifi className="h-4 w-4 text-gray-500" />
                <span className="text-body-medium text-gray-700">Network</span>
              </div>
              <span className={`text-body-medium font-medium ${
                metrics.networkLatency > 500 ? 'text-red-600' :
                metrics.networkLatency > 200 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {metrics.networkLatency === -1 ? 'N/A' : `${metrics.networkLatency.toFixed(0)}ms`}
              </span>
            </div>

            {/* FPS */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-gray-500" />
                <span className="text-body-medium text-gray-700">FPS</span>
              </div>
              <span className={`text-body-medium font-medium ${
                metrics.fps < 30 ? 'text-red-600' :
                metrics.fps < 50 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {metrics.fps}
              </span>
            </div>

            {/* Bundle Size */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Database className="h-4 w-4 text-gray-500" />
                <span className="text-body-medium text-gray-700">Bundle</span>
              </div>
              <span className="text-body-medium font-medium text-gray-600">
                ~{metrics.bundleSize}KB
              </span>
            </div>
          </div>

          {/* Performance Tips */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <h4 className="text-label-medium text-gray-700 mb-2">Quick Tips</h4>
            <div className="space-y-1 text-body-small text-gray-600">
              {metrics.loadTime > 3000 && (
                <div>• Consider code splitting for faster loads</div>
              )}
              {metrics.memoryUsage > 100 && (
                <div>• High memory usage detected</div>
              )}
              {metrics.fps < 30 && (
                <div>• Low FPS - check for heavy animations</div>
              )}
              {metrics.networkLatency > 500 && (
                <div>• Slow network detected</div>
              )}
            </div>
          </div>

          {/* Close Button */}
          <button
            onClick={() => setIsVisible(false)}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            ×
          </button>
        </div>
      )}
    </>
  )
}

// Hook for measuring component render time
export function useRenderTime(componentName: string) {
  const renderStart = useRef<number>(0)
  const [renderTime, setRenderTime] = useState<number>(0)

  useEffect(() => {
    renderStart.current = performance.now()
  })

  useEffect(() => {
    if (renderStart.current) {
      const time = performance.now() - renderStart.current
      setRenderTime(time)

      if (process.env.NODE_ENV === 'development') {
        console.log(`🎨 ${componentName} render time: ${time.toFixed(2)}ms`)
      }
    }
  }, [componentName])

  return renderTime
}

// Hook for measuring API call performance
export function useApiPerformance() {
  const measureApiCall = async (
    apiCall: () => Promise<any>,
    endpoint: string
  ) => {
    const start = performance.now()
    
    try {
      const result = await apiCall()
      const duration = performance.now() - start
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🌐 API ${endpoint}: ${duration.toFixed(2)}ms`)
      }
      
      return { result, duration, success: true }
    } catch (error) {
      const duration = performance.now() - start
      
      if (process.env.NODE_ENV === 'development') {
        console.error(`🌐 API ${endpoint} failed: ${duration.toFixed(2)}ms`, error)
      }
      
      return { error, duration, success: false }
    }
  }

  return { measureApiCall }
}
