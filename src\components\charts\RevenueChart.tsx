'use client'

import { useEffect, useRef, useState } from 'react'
import * as echarts from 'echarts'
import { DollarSign, TrendingUp, Calendar } from 'lucide-react'

interface RevenueData {
  month: string
  revenue: number
  profit: number
  expenses: number
}

interface RevenueChartProps {
  data: RevenueData[]
  title?: string
  height?: number
  showProfitMargin?: boolean
}

export default function RevenueChart({
  data,
  title = "Revenue Trends",
  height = 350
}: RevenueChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'3M' | '6M' | '1Y'>('6M')

  // Calculate metrics
  const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0)
  const totalProfit = data.reduce((sum, item) => sum + item.profit, 0)
  const profitMargin = ((totalProfit / totalRevenue) * 100).toFixed(1)


  // Growth calculation
  const firstMonth = data[0]?.revenue || 0
  const lastMonth = data[data.length - 1]?.revenue || 0
  const growthRate = firstMonth > 0 ? (((lastMonth - firstMonth) / firstMonth) * 100).toFixed(1) : '0'

  useEffect(() => {
    if (!chartRef.current || !data.length) return

    chartInstance.current = echarts.init(chartRef.current, 'light')

    const option: echarts.EChartsOption = {
      title: {
        text: title,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#374151'
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: {
          color: '#374151'
        },
        formatter: function (params: any) {
          const month = params[0].axisValue
          let content = `<div style="padding: 8px;"><div style="font-weight: bold; margin-bottom: 8px;">${month}</div>`

          params.forEach((param: any) => {
            const color = param.color
            const value = param.seriesName === 'Profit Margin' 
              ? `${param.value}%` 
              : `₱${param.value.toLocaleString()}`
            content += `<div style="margin: 4px 0;"><span style="color: ${color};">●</span> ${param.seriesName}: ${value}</div>`
          })
          
          return content + '</div>'
        }
      },
      legend: {
        data: ['Revenue', 'Profit', 'Expenses', 'Profit Margin'],
        top: 35,
        textStyle: {
          color: '#6b7280'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.month),
        axisLine: {
          lineStyle: {
            color: '#e5e7eb'
          }
        },
        axisLabel: {
          color: '#6b7280'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: 'Amount (₱)',
          position: 'left',
          axisLine: {
            lineStyle: {
              color: '#e5e7eb'
            }
          },
          axisLabel: {
            color: '#6b7280',
            formatter: '₱{value}'
          },
          splitLine: {
            lineStyle: {
              color: '#f3f4f6'
            }
          }
        },
        {
          type: 'value',
          name: 'Margin (%)',
          position: 'right',
          axisLine: {
            lineStyle: {
              color: '#e5e7eb'
            }
          },
          axisLabel: {
            color: '#6b7280',
            formatter: '{value}%'
          }
        }
      ],
      series: [
        {
          name: 'Revenue',
          type: 'bar',
          data: data.map(item => item.revenue),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#34d399' },
              { offset: 1, color: '#10b981' }
            ]),
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#6ee7b7' },
                { offset: 1, color: '#34d399' }
              ])
            }
          }
        },
        {
          name: 'Profit',
          type: 'bar',
          data: data.map(item => item.profit),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#60a5fa' },
              { offset: 1, color: '#3b82f6' }
            ]),
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: 'Expenses',
          type: 'bar',
          data: data.map(item => item.expenses),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#f87171' },
              { offset: 1, color: '#ef4444' }
            ]),
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: 'Profit Margin',
          type: 'line',
          yAxisIndex: 1,
          data: data.map(item => ((item.profit / item.revenue) * 100).toFixed(1)),
          lineStyle: {
            color: '#8b5cf6',
            width: 3
          },
          itemStyle: {
            color: '#8b5cf6'
          },
          symbol: 'circle',
          symbolSize: 6
        }
      ],
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut'
    }

    chartInstance.current.setOption(option)
    setTimeout(() => setIsLoading(false), 600)

    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [data, title, height])

  return (
    <div className="card-elevated p-6 hover-lift animate-fade-in stagger-2">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-2 rounded-lg">
            <DollarSign className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-heading-medium text-gray-900">{title}</h3>
            <p className="text-body-small text-muted">Financial performance overview</p>
          </div>
        </div>

        {/* Time Range Selector */}
        <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
          {(['3M', '6M', '1Y'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`
                px-3 py-1 text-xs font-medium rounded-md transition-all duration-200
                ${timeRange === range
                  ? 'bg-white text-primary shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
                }
              `}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Chart Container */}
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
              <span className="text-body-medium text-muted">Loading revenue data...</span>
            </div>
          </div>
        )}
        <div
          ref={chartRef}
          style={{ height: `${height}px`, width: '100%' }}
          className="transition-opacity duration-300"
        />
      </div>

      {/* Metrics Summary */}
      <div className="mt-6 grid grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-100">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <DollarSign className="h-4 w-4 text-green-600" />
            <p className="text-body-small text-muted">Total Revenue</p>
          </div>
          <p className="text-heading-small text-gray-900">₱{totalRevenue.toLocaleString()}</p>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <TrendingUp className="h-4 w-4 text-blue-600" />
            <p className="text-body-small text-muted">Total Profit</p>
          </div>
          <p className="text-heading-small text-gray-900">₱{totalProfit.toLocaleString()}</p>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <Calendar className="h-4 w-4 text-purple-600" />
            <p className="text-body-small text-muted">Profit Margin</p>
          </div>
          <p className="text-heading-small text-purple-600">{profitMargin}%</p>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <TrendingUp className="h-4 w-4 text-emerald-600" />
            <p className="text-body-small text-muted">Growth Rate</p>
          </div>
          <p className={`text-heading-small ${parseFloat(growthRate) >= 0 ? 'text-success' : 'text-error'}`}>
            {parseFloat(growthRate) >= 0 ? '+' : ''}{growthRate}%
          </p>
        </div>
      </div>
    </div>
  )
}
