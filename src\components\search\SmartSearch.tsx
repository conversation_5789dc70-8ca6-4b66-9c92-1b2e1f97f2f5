'use client'

import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { Search, X, Filter, Clock, Package, Users, CreditCard } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface SearchResult {
  id: string
  title: string
  subtitle: string
  category: string
  url: string
  icon: React.ComponentType<{ className?: string }>
  metadata?: string
}

interface SmartSearchProps {
  placeholder?: string
  showFilters?: boolean
  onSearch?: (query: string, filters: SearchFilters) => void
  className?: string
}

interface SearchFilters {
  category: string
  dateRange: string
  status: string
  sortBy: string
}

export default function SmartSearch({
  placeholder = "Search products, customers, transactions...",
  showFilters = true,
  onSearch,
  className = ""
}: SmartSearchProps) {
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [filters, setFilters] = useState<SearchFilters>({
    category: 'all',
    dateRange: 'all',
    status: 'all',
    sortBy: 'relevance'
  })

  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  // Sample data - In production, this would come from your API
  const sampleResults = useMemo(() => [
    {
      id: '1',
      title: 'Coca Cola 1.5L',
      subtitle: 'Beverages • In Stock',
      category: 'products',
      url: '/products/1',
      icon: Package,
      metadata: '₱45.00 • 24 units'
    },
    {
      id: '2',
      title: 'Juan Dela Cruz',
      subtitle: 'Regular Customer',
      category: 'customers',
      url: '/customers/2',
      icon: Users,
      metadata: '₱1,250 total purchases'
    },
    {
      id: '3',
      title: 'Maria Santos - Outstanding Debt',
      subtitle: 'Due: Dec 15, 2024',
      category: 'debts',
      url: '/debts/3',
      icon: CreditCard,
      metadata: '₱350.00 remaining'
    }
  ], [])

  // Search function
  const performSearch = useCallback((searchQuery: unknown) => {
    const query = String(searchQuery)
    if (query.length > 0) {
      setIsLoading(true)
      // Simulate API call
      setTimeout(() => {
        const filtered = sampleResults.filter(item =>
          item.title.toLowerCase().includes(query.toLowerCase()) ||
          item.subtitle.toLowerCase().includes(query.toLowerCase())
        )
        setResults(filtered)
        setIsLoading(false)
      }, 300)
    } else {
      setResults([])
      setIsLoading(false)
    }
  }, [sampleResults])

  // Create debounced version using useRef to avoid dependency issues
  const debouncedSearchRef = useRef(debounce(performSearch, 300))

  // Update the debounced function when performSearch changes
  useEffect(() => {
    debouncedSearchRef.current = debounce(performSearch, 300)
  }, [performSearch])

  useEffect(() => {
    debouncedSearchRef.current(query)
  }, [query])

  useEffect(() => {
    // Load recent searches from localStorage
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }, [])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery)
    setIsOpen(true)
    
    if (onSearch) {
      onSearch(searchQuery, filters)
    }
  }

  const handleResultClick = (result: SearchResult) => {
    // Save to recent searches
    const updated = [result.title, ...recentSearches.filter(s => s !== result.title)].slice(0, 5)
    setRecentSearches(updated)
    localStorage.setItem('recentSearches', JSON.stringify(updated))
    
    setIsOpen(false)
    router.push(result.url)
  }

  const clearSearch = () => {
    setQuery('')
    setResults([])
    setIsOpen(false)
    inputRef.current?.focus()
  }



  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'products': return 'text-green-600 bg-green-100'
      case 'customers': return 'text-green-600 bg-green-100'
      case 'debts': return 'text-red-600 bg-red-100'
      case 'payments': return 'text-purple-600 bg-purple-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => handleSearch(e.target.value)}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className="
            block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-xl
            bg-white text-gray-900 placeholder-gray-500
            focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent
            transition-all duration-200
            hover:border-gray-400
          "
        />
        
        {/* Clear Button */}
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 transition-colors"
          >
            <X className="h-5 w-5 text-gray-400" />
          </button>
        )}

        {/* Filter Button */}
        {showFilters && (
          <button
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className={`
              absolute inset-y-0 right-10 pr-3 flex items-center transition-colors
              ${showAdvancedFilters ? 'text-primary' : 'text-gray-400 hover:text-gray-600'}
            `}
          >
            <Filter className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg p-4 z-50 animate-fade-in">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <label className="text-label-medium text-gray-700 mb-2 block">Category</label>
              <select
                value={filters.category}
                onChange={(e) => setFilters({...filters, category: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="all">All Categories</option>
                <option value="products">Products</option>
                <option value="customers">Customers</option>
                <option value="debts">Debts</option>
                <option value="payments">Payments</option>
              </select>
            </div>
            
            <div>
              <label className="text-label-medium text-gray-700 mb-2 block">Date Range</label>
              <select
                value={filters.dateRange}
                onChange={(e) => setFilters({...filters, dateRange: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="year">This Year</option>
              </select>
            </div>
            
            <div>
              <label className="text-label-medium text-gray-700 mb-2 block">Status</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters({...filters, status: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="completed">Completed</option>
              </select>
            </div>
            
            <div>
              <label className="text-label-medium text-gray-700 mb-2 block">Sort By</label>
              <select
                value={filters.sortBy}
                onChange={(e) => setFilters({...filters, sortBy: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="relevance">Relevance</option>
                <option value="date">Date</option>
                <option value="name">Name</option>
                <option value="amount">Amount</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Search Results Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-50 max-h-96 overflow-y-auto animate-fade-in">
          {/* Loading State */}
          {isLoading && (
            <div className="p-4 text-center">
              <div className="inline-flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                <span className="text-body-medium text-muted">Searching...</span>
              </div>
            </div>
          )}

          {/* Recent Searches */}
          {!query && recentSearches.length > 0 && (
            <div className="p-4 border-b border-gray-100">
              <h3 className="text-label-large text-gray-900 mb-3 flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                Recent Searches
              </h3>
              <div className="space-y-2">
                {recentSearches.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleSearch(search)}
                    className="block w-full text-left px-3 py-2 text-body-medium text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    {search}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Search Results */}
          {results.length > 0 && (
            <div className="p-2">
              {results.map((result) => {
                const IconComponent = result.icon
                return (
                  <button
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className="w-full flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors group"
                  >
                    <div className={`p-2 rounded-lg ${getCategoryColor(result.category)}`}>
                      <IconComponent className="h-4 w-4" />
                    </div>
                    <div className="flex-1 text-left">
                      <h4 className="text-body-medium font-medium text-gray-900 group-hover:text-primary transition-colors">
                        {result.title}
                      </h4>
                      <p className="text-body-small text-muted">{result.subtitle}</p>
                      {result.metadata && (
                        <p className="text-body-small text-gray-500 mt-1">{result.metadata}</p>
                      )}
                    </div>
                  </button>
                )
              })}
            </div>
          )}

          {/* No Results */}
          {query && !isLoading && results.length === 0 && (
            <div className="p-8 text-center">
              <Search className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-heading-small text-gray-900 mb-2">No results found</h3>
              <p className="text-body-medium text-muted">
                Try adjusting your search terms or filters
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// Debounce utility function
function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
