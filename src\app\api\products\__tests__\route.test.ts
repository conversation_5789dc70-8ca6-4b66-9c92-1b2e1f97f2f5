import { GET, POST } from '../route'
import { prisma } from '@/lib/prisma'
import { NextRequest } from 'next/server'

// Mock Prisma
// Mock the entire prisma module
jest.mock('@/lib/prisma', () => ({
  prisma: {
    product: {
      findMany: jest.fn(),
      create: jest.fn(),
    },
  },
}))

// Create a properly typed mock
const mockPrisma = {
  product: {
    findMany: jest.fn(),
    create: jest.fn(),
  },
} as any

describe('/api/products', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('GET /api/products', () => {
    it('should return all products', async () => {
      const mockProducts = [
        {
          id: '1',
          name: 'Test Product 1',
          netWeight: '100g',
          price: 25.50,
          stock: 10,
          category: 'Snacks',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          name: 'Test Product 2',
          netWeight: '200g',
          price: 45.00,
          stock: 5,
          category: 'Beverages',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]

      mockPrisma.product.findMany.mockResolvedValue(mockProducts)

      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toEqual(mockProducts)
      expect(mockPrisma.product.findMany).toHaveBeenCalledWith({
        orderBy: { createdAt: 'desc' },
      })
    })

    it('should handle database errors', async () => {
      mockPrisma.product.findMany.mockRejectedValue(new Error('Database error'))

      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to fetch products')
    })
  })

  describe('POST /api/products', () => {
    it('should create a new product', async () => {
      const newProduct = {
        name: 'New Product',
        netWeight: '150g',
        price: 35.00,
        stock: 20,
        category: 'Snacks',
      }

      const createdProduct = {
        id: '3',
        ...newProduct,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockPrisma.product.create.mockResolvedValue(createdProduct)

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(newProduct),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data).toEqual(createdProduct)
      expect(mockPrisma.product.create).toHaveBeenCalledWith({
        data: newProduct,
      })
    })

    it('should reject invalid product data', async () => {
      const invalidProduct = {
        name: '', // Empty name should fail validation
        netWeight: '150g',
        price: 35.00,
        stock: 20,
        category: 'Snacks',
      }

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(invalidProduct),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid product data')
    })

    it('should handle database creation errors', async () => {
      const newProduct = {
        name: 'New Product',
        netWeight: '150g',
        price: 35.00,
        stock: 20,
        category: 'Snacks',
      }

      mockPrisma.product.create.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(newProduct),
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to create product')
    })
  })
})
