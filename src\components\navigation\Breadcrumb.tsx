'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { ChevronRight, Home, Package, Users, CreditCard, Receipt, BarChart3, FileText, Settings } from 'lucide-react'
import { useState, useEffect, useMemo } from 'react'

interface BreadcrumbItem {
  label: string
  href: string
  icon?: React.ComponentType<{ className?: string }>
  isActive?: boolean
}

interface BreadcrumbProps {
  customItems?: BreadcrumbItem[]
  showHome?: boolean
  maxItems?: number
  className?: string
}

export default function Breadcrumb({
  customItems,
  showHome = true,
  maxItems = 5,
  className = ""
}: BreadcrumbProps) {
  const pathname = usePathname()
  const [breadcrumbItems, setBreadcrumbItems] = useState<BreadcrumbItem[]>([])

  // Route configuration for automatic breadcrumb generation
  const routeConfig = useMemo(() => ({
    '/dashboard': { label: 'Dashboard', icon: Home },
    '/analytics': { label: 'Analytics', icon: BarChart3 },
    '/products': { label: 'Products', icon: Package },
    '/products/new': { label: 'Add Product', icon: Package },
    '/customers': { label: 'Customers', icon: Users },
    '/customers/new': { label: 'Add Customer', icon: Users },
    '/debts': { label: 'Debts', icon: CreditCard },
    '/debts/new': { label: 'Record Debt', icon: CreditCard },
    '/payments': { label: 'Payments', icon: Receipt },
    '/payments/new': { label: 'Record Payment', icon: Receipt },
    '/reports': { label: 'Reports', icon: FileText },
    '/settings': { label: 'Settings', icon: Settings },
  }), [])

  useEffect(() => {
    if (customItems) {
      setBreadcrumbItems(customItems)
      return
    }

    // Generate breadcrumbs from current path
    const pathSegments = pathname.split('/').filter(segment => segment !== '')
    const items: BreadcrumbItem[] = []

    // Add home if requested
    if (showHome && pathname !== '/dashboard') {
      items.push({
        label: 'Dashboard',
        href: '/dashboard',
        icon: Home
      })
    }

    // Build breadcrumb path
    let currentPath = ''
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`
      const isLast = index === pathSegments.length - 1
      
      // Check if this is a dynamic route (like /products/123)
      const isDynamicRoute = !routeConfig[currentPath] && index > 0
      
      if (isDynamicRoute) {
        // For dynamic routes, use the segment as label and make it a link to the parent
        const parentPath = currentPath.split('/').slice(0, -1).join('/')
        const parentConfig = routeConfig[parentPath]
        
        items.push({
          label: segment.charAt(0).toUpperCase() + segment.slice(1),
          href: currentPath,
          icon: parentConfig?.icon,
          isActive: isLast
        })
      } else {
        const config = routeConfig[currentPath]
        if (config) {
          items.push({
            label: config.label,
            href: currentPath,
            icon: config.icon,
            isActive: isLast
          })
        }
      }
    })

    // Limit items if maxItems is set
    if (items.length > maxItems) {
      const truncatedItems = [
        items[0], // Keep first item (usually home)
        { label: '...', href: '', isActive: false }, // Ellipsis
        ...items.slice(-2) // Keep last 2 items
      ]
      setBreadcrumbItems(truncatedItems)
    } else {
      setBreadcrumbItems(items)
    }
  }, [pathname, customItems, showHome, maxItems, routeConfig])

  if (breadcrumbItems.length === 0) {
    return null
  }

  return (
    <nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {breadcrumbItems.map((item, index) => {
          const isLast = index === breadcrumbItems.length - 1
          const IconComponent = item.icon

          return (
            <li key={index} className="flex items-center">
              {index > 0 && (
                <ChevronRight className="h-4 w-4 text-gray-400 mx-2 flex-shrink-0" />
              )}
              
              {item.label === '...' ? (
                <span className="text-gray-400 px-2">...</span>
              ) : isLast || item.isActive ? (
                <span className="flex items-center space-x-1.5 text-gray-900 font-medium">
                  {IconComponent && (
                    <IconComponent className="h-4 w-4 text-gray-600" />
                  )}
                  <span className="truncate max-w-[150px]">{item.label}</span>
                </span>
              ) : (
                <Link
                  href={item.href}
                  className="flex items-center space-x-1.5 text-gray-500 hover:text-gray-700 transition-colors duration-200 rounded-md px-2 py-1 hover:bg-gray-100"
                >
                  {IconComponent && (
                    <IconComponent className="h-4 w-4" />
                  )}
                  <span className="truncate max-w-[150px]">{item.label}</span>
                </Link>
              )}
            </li>
          )
        })}
      </ol>
    </nav>
  )
}

// Enhanced Breadcrumb with additional features
interface EnhancedBreadcrumbProps extends BreadcrumbProps {
  showBackButton?: boolean
  onBack?: () => void
  showPageActions?: boolean
  pageActions?: React.ReactNode
}

export function EnhancedBreadcrumb({
  showBackButton = false,
  onBack,
  showPageActions = false,
  pageActions,
  ...breadcrumbProps
}: EnhancedBreadcrumbProps) {
  const handleBack = () => {
    if (onBack) {
      onBack()
    } else {
      window.history.back()
    }
  }

  return (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center space-x-4">
        {showBackButton && (
          <button
            onClick={handleBack}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors duration-200 rounded-lg px-3 py-2 hover:bg-gray-100"
          >
            <ChevronRight className="h-4 w-4 rotate-180" />
            <span className="text-sm font-medium">Back</span>
          </button>
        )}
        <Breadcrumb {...breadcrumbProps} />
      </div>
      
      {showPageActions && pageActions && (
        <div className="flex items-center space-x-2">
          {pageActions}
        </div>
      )}
    </div>
  )
}

// Breadcrumb with page context
interface PageBreadcrumbProps {
  title: string
  subtitle?: string
  icon?: React.ComponentType<{ className?: string }>
  actions?: React.ReactNode
  breadcrumbItems?: BreadcrumbItem[]
}

export function PageBreadcrumb({
  title,
  subtitle,
  icon: IconComponent,
  actions,
  breadcrumbItems
}: PageBreadcrumbProps) {
  return (
    <div className="space-y-4">
      {/* Breadcrumb Navigation */}
      <Breadcrumb customItems={breadcrumbItems} />
      
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {IconComponent && (
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-xl">
              <IconComponent className="h-6 w-6 text-white" />
            </div>
          )}
          <div>
            <h1 className="text-display-medium text-gray-900">{title}</h1>
            {subtitle && (
              <p className="text-body-medium text-muted mt-1">{subtitle}</p>
            )}
          </div>
        </div>
        
        {actions && (
          <div className="flex items-center space-x-3">
            {actions}
          </div>
        )}
      </div>
    </div>
  )
}

// Hook for managing breadcrumb state
export function useBreadcrumb() {
  const pathname = usePathname()
  const [history, setHistory] = useState<string[]>([])

  useEffect(() => {
    setHistory(prev => {
      const newHistory = [...prev]
      if (!newHistory.includes(pathname)) {
        newHistory.push(pathname)
        // Keep only last 10 pages
        return newHistory.slice(-10)
      }
      return newHistory
    })
  }, [pathname])

  const goBack = () => {
    if (history.length > 1) {
      const previousPage = history[history.length - 2]
      window.location.href = previousPage
    } else {
      window.history.back()
    }
  }

  const canGoBack = history.length > 1

  return {
    history,
    goBack,
    canGoBack,
    currentPath: pathname
  }
}
