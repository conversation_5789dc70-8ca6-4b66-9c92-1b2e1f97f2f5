'use client'

import { useState, useEffect, useMemo } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { Clock, X, ArrowLeft, ExternalLink } from 'lucide-react'

interface HistoryItem {
  path: string
  title: string
  timestamp: number
  icon?: string
}

interface NavigationHistoryProps {
  maxItems?: number
  showTimestamp?: boolean
  className?: string
}

export default function NavigationHistory({
  maxItems = 10,
  showTimestamp = true,
  className = ""
}: NavigationHistoryProps) {
  const pathname = usePathname()
  const [history, setHistory] = useState<HistoryItem[]>([])
  const [isOpen, setIsOpen] = useState(false)

  // Page titles mapping
  const pageTitles = useMemo((): Record<string, string> => ({
    '/dashboard': 'Dashboard',
    '/analytics': 'Analytics',
    '/products': 'Products',
    '/products/new': 'Add Product',
    '/customers': 'Customers',
    '/customers/new': 'Add Customer',
    '/debts': 'Debts',
    '/debts/new': 'Record Debt',
    '/payments': 'Payments',
    '/payments/new': 'Record Payment',
    '/reports': 'Reports',
    '/settings': 'Settings',
  }), [])

  useEffect(() => {
    // Load history from localStorage
    const savedHistory = localStorage.getItem('navigationHistory')
    if (savedHistory) {
      try {
        setHistory(JSON.parse(savedHistory))
      } catch (error) {
        console.error('Error loading navigation history:', error)
      }
    }
  }, [])

  useEffect(() => {
    // Add current page to history
    const title = pageTitles[pathname] || pathname.split('/').pop() || 'Unknown Page'
    const newItem: HistoryItem = {
      path: pathname,
      title,
      timestamp: Date.now()
    }

    setHistory(prev => {
      // Don't add if it's the same as the last item
      if (prev.length > 0 && prev[prev.length - 1].path === pathname) {
        return prev
      }

      const updated = [...prev, newItem].slice(-maxItems)
      
      // Save to localStorage
      try {
        localStorage.setItem('navigationHistory', JSON.stringify(updated))
      } catch (error) {
        console.error('Error saving navigation history:', error)
      }
      
      return updated
    })
  }, [pathname, maxItems, pageTitles])

  const clearHistory = () => {
    setHistory([])
    localStorage.removeItem('navigationHistory')
  }

  const removeItem = (index: number) => {
    const updated = history.filter((_, i) => i !== index)
    setHistory(updated)
    localStorage.setItem('navigationHistory', JSON.stringify(updated))
  }

  const formatTimestamp = (timestamp: number) => {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  if (history.length === 0) {
    return null
  }

  return (
    <div className={`relative ${className}`}>
      {/* History Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors duration-200"
        title="Navigation History"
      >
        <Clock className="h-4 w-4" />
        <span className="text-sm font-medium">Recent</span>
        {history.length > 0 && (
          <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
            {history.length}
          </span>
        )}
      </button>

      {/* History Dropdown */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          
          {/* History Panel */}
          <div className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-xl shadow-xl z-50 animate-fade-in">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-heading-small text-gray-900">Navigation History</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={clearHistory}
                  className="text-body-small text-gray-500 hover:text-gray-700 transition-colors"
                >
                  Clear all
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* History Items */}
            <div className="max-h-96 overflow-y-auto">
              {history.slice().reverse().map((item, index) => {
                const isCurrentPage = item.path === pathname
                const reverseIndex = history.length - 1 - index

                return (
                  <div
                    key={`${item.path}-${item.timestamp}`}
                    className={`
                      group flex items-center justify-between p-3 hover:bg-gray-50 transition-colors
                      ${isCurrentPage ? 'bg-blue-50 border-l-4 border-blue-500' : ''}
                    `}
                  >
                    <Link
                      href={item.path}
                      onClick={() => setIsOpen(false)}
                      className="flex-1 flex items-center space-x-3 min-w-0"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className={`
                            text-body-medium font-medium truncate
                            ${isCurrentPage ? 'text-blue-900' : 'text-gray-900'}
                          `}>
                            {item.title}
                          </p>
                          {isCurrentPage && (
                            <span className="text-body-small text-blue-600 font-medium">
                              Current
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          <p className="text-body-small text-gray-500 truncate">
                            {item.path}
                          </p>
                          {showTimestamp && (
                            <>
                              <span className="text-gray-300">•</span>
                              <p className="text-body-small text-gray-400">
                                {formatTimestamp(item.timestamp)}
                              </p>
                            </>
                          )}
                        </div>
                      </div>
                      <ExternalLink className="h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </Link>

                    {!isCurrentPage && (
                      <button
                        onClick={(e) => {
                          e.preventDefault()
                          removeItem(reverseIndex)
                        }}
                        className="ml-2 p-1 text-gray-400 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-all"
                        title="Remove from history"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    )}
                  </div>
                )
              })}
            </div>

            {/* Footer */}
            <div className="p-3 border-t border-gray-200 bg-gray-50">
              <p className="text-body-small text-gray-500 text-center">
                Showing last {Math.min(history.length, maxItems)} pages
              </p>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

// Quick Navigation Component
export function QuickNavigation() {
  const { goBack, canGoBack } = useBreadcrumb()

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={goBack}
        disabled={!canGoBack}
        className={`
          flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors duration-200
          ${canGoBack
            ? 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            : 'text-gray-400 cursor-not-allowed'
          }
        `}
        title="Go back"
      >
        <ArrowLeft className="h-4 w-4" />
        <span className="text-sm font-medium">Back</span>
      </button>
      
      <NavigationHistory />
    </div>
  )
}

// Hook for navigation history
function useBreadcrumb() {
  const pathname = usePathname()
  const [history, setHistory] = useState<string[]>([])

  useEffect(() => {
    const savedHistory = localStorage.getItem('pageHistory')
    if (savedHistory) {
      try {
        setHistory(JSON.parse(savedHistory))
      } catch (error) {
        console.error('Error loading page history:', error)
      }
    }
  }, [])

  useEffect(() => {
    setHistory(prev => {
      const newHistory = [...prev]
      if (!newHistory.includes(pathname)) {
        newHistory.push(pathname)
        const updated = newHistory.slice(-20) // Keep last 20 pages
        localStorage.setItem('pageHistory', JSON.stringify(updated))
        return updated
      }
      return newHistory
    })
  }, [pathname])

  const goBack = () => {
    if (history.length > 1) {
      const previousPage = history[history.length - 2]
      window.location.href = previousPage
    } else {
      window.history.back()
    }
  }

  const canGoBack = history.length > 1

  return {
    history,
    goBack,
    canGoBack,
    currentPath: pathname
  }
}
